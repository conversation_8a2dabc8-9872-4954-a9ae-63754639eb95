.PHONY: install lint test clean docs all

install:
	python3 -m venv .venv_tetris && . .venv_tetris/bin/activate && \
	pip install --upgrade pip && \
	pip install -e .[dev]

lint:
	. .venv_tetris/bin/activate && flake8 src tests
	. .venv_tetris/bin/activate && mypy src tests

test:
	. .venv_tetris/bin/activate && pytest --cov=src --cov-report=term

docs:
	. .venv_tetris/bin/activate && cd docs && make html

clean:
	rm -rf .venv_tetris
	rm -rf src/*.egg-info
	rm -rf build/
	rm -rf dist/
	rm -rf .pytest_cache/
	rm -rf .coverage
	rm -rf docs/_build/
	find . -type d -name __pycache__ -exec rm -rf {} +
	find . -type f -name "*.pyc" -delete

all: install lint test docs
