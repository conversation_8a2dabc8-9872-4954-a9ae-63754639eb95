from tetris_engine.io import process_file


def test_process_file(tmp_path):
    inp = tmp_path/"in.txt"
    out = tmp_path/"out.txt"
    inp.write_text("I0\nQ0,Q2,Q4,Q6,Q8\n")
    process_file(str(inp), str(out))
    assert out.read_text().splitlines() == ['1', '0']

def test_process_file_empty(tmp_path):
    inp = tmp_path/"empty.txt"
    out = tmp_path/"out.txt"
    inp.write_text("\n  \n")
    process_file(str(inp), str(out))
    assert out.read_text() == ""


def test_process_file_stdout(tmp_path, capsys):
    inp = tmp_path/"in.txt"
    inp.write_text("I0\nQ0,Q2,Q4,Q6,Q8\n")
    process_file(str(inp))  # No output file specified
    captured = capsys.readouterr()
    assert captured.out.splitlines() == ['1', '0']
