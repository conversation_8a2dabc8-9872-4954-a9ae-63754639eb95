import pytest
from tetris.core import Board
from tetris.constants import TETROMINO


@pytest.mark.parametrize(
    "shape,left_col,exp_h",
    [
        ("I", 0, 1),
        ("T", 0, 2),
        ("S", 0, 2),
        ("Z", 0, 2),
        ("Q", 0, 2),
        ("L", 0, 3),
        ("J", 0, 3),
    ],
)
def test_single_piece_drop(shape, left_col, exp_h):
    b = Board()
    assert b.drop(shape, left_col) == exp_h


@pytest.mark.parametrize(
    "shape,left_col,cols",
    [
        ("I", 3, [3, 4, 5, 6]),
        ("T", 1, [1, 2, 3]),
        ("Q", 4, [4, 5]),
    ],
)
def test_occupied_columns(shape, left_col, cols):
    b = Board()
    b.drop(shape, left_col)
    xs = sorted(set(x for x, _ in b.occupied))
    assert xs == cols


def test_stack_two_drops():
    b = Board()
    h1 = b.drop("I", 0)
    h2 = b.drop("I", 0)
    assert (h1, h2) == (1, 2)


@pytest.mark.parametrize(
    "shape,max_c",
    [
        ("I", 6),
        ("T", 7),
        ("S", 7),
        ("Z", 7),
        ("Q", 8),
        ("L", 8),
        ("J", 8),
    ],
)
def test_right_boundary(shape, max_c):
    b = Board()
    h = b.drop(shape, max_c)
    expected = max(dy for _, dy in TETROMINO[shape]) + 1
    assert h == expected


def test_full_board_clear():
    b = Board()
    for col in [0, 2, 4, 6, 8]:
        h = b.drop("Q", col)
    assert h == 0
    assert b.occupied == set()


def test_multi_row_clear_then_drop():
    b = Board()
    for col in [0, 2, 4, 6, 8]:
        b.drop("Q", col)
    assert b.drop("I", 0) == 1


def test_no_clear_sequence():
    b = Board()
    seq = ["T", "S", "Z", "L", "J"]
    heights = [b.drop(s, i % 7) for i, s in enumerate(seq, 1)]
    assert heights == sorted(heights)
