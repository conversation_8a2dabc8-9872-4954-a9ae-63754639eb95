.PHONY: install lint test clean all

install:
	python3 -m venv .venv_tetris && . .venv_tetris/bin/activate && \
	pip install --upgrade pip && \
	pip install -e .[dev]

lint:
	flake8 src tests
	mypy src tests

test:
	pytest --cov=src --cov-report=term

clean:
	rm -rf .venv_tetris
	rm -rf src/*.egg-info
	rm -rf build/
	rm -rf dist/
	rm -rf .pytest_cache/
	rm -rf .coverage
	find . -type d -name __pycache__ -exec rm -rf {} +
	find . -type f -name "*.pyc" -delete

all: install lint test
