[build-system]
requires = ["setuptools", "wheel"]
build-backend = "setuptools.build_meta"

[project]
name = "tetris-engine"
version = "0.1.0"
description = "Simplified Tetris Engine"
readme = "README.md"
requires-python = ">=3.7"
dependencies = []

[tool.setuptools.packages.find]
where = ["src"]

[tool.setuptools.package-dir]
"" = "src"

[project.optional-dependencies]
# Core library has no external dependencies
# Testing: pytest and pytest-cov are required for running tests
# Development: flake8 and mypy are optional for code quality but don't affect functionality
dev = [
    "pytest>=6.0",      # Required for testing framework
    "pytest-cov",       # Required for coverage reporting
    "flake8",           # Optional: code linting
    "mypy"              # Optional: static type checking
]

[tool.pytest.ini_options]
minversion = "6.0"
testpaths = ["tests"]
pythonpath = ["src"]