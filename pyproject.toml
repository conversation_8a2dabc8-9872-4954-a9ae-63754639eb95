[build-system]
requires = ["setuptools", "wheel"]
build-backend = "setuptools.build_meta"

[project]
name = "tetris-processor"
version = "0.1.0"
description = "Simplified Tetris Engine"
readme = "README.md"
requires-python = ">=3.7"
dependencies = []

[tool.setuptools.packages.find]
where = ["src"]

[tool.setuptools.package-dir]
"" = "src"

[project.optional-dependencies]
dev = [
    "pytest>=6.0",
    "pytest-cov",
    "flake8",
    "mypy"
]

[tool.pytest.ini_options]
minversion = "6.0"
testpaths = ["tests"]
pythonpath = ["src"]