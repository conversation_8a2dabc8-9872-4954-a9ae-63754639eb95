Metadata-Version: 2.4
Name: tetris-engine
Version: 0.1.0
Summary: Simplified Tetris Engine
Requires-Python: >=3.7
Description-Content-Type: text/markdown
License-File: LICENSE
Provides-Extra: dev
Requires-Dist: pytest>=6.0; extra == "dev"
Requires-Dist: pytest-cov; extra == "dev"
Requires-Dist: flake8; extra == "dev"
Requires-Dist: mypy; extra == "dev"
Dynamic: license-file

# Simplified Tetris Processor

This package implements a simplified Tetris engine:
- **7 Tetromino types** (I, T, S, Z, Q, L, J), no rotations.
- **10-column** board, unbounded height.
- **Line clears** remove full rows and shift above rows down.

and this markdown serves as the overall instruction manual.

## Installation

```bash
make install
```

## IO format

### Input format
As instructed in the problem statement, each line of the input file is a comma-separated list. Each entry in the list is a single letter `(I, T, S, Z, Q, L, J)` and a single-digit integer (`[0,9]`). The integer represents the left-most column of the grid that the shape occupies, starting from zero.

### Output format
One integer per line in the output file—final board height corresponding to each line of input Tetris sequence.

A sample is provided in `examples/sample_input.txt`:
```
Q0
Q0,Q1
Q0,Q2,Q4,Q6,Q8
Q0,Q2,Q4,Q6,Q8,Q1
Q0,Q2,Q4,Q6,Q8,Q1,Q1
I0,I4,Q8
I0,I4,Q8,I0,I4
L0,J2,L4,J6,Q8
L0,Z1,Z3,Z5,Z7
T0,T3
T0,T3,I6,I6
I0,I6,S4
T1,Z3,I4
L0,J3,L5,J8,T1
L0,J3,L5,J8,T1,T6
L0,J3,L5,J8,T1,T6,J2,L6,T0,T7
L0,J3,L5,J8,T1,T6,J2,L6,T0,T7,Q4
S0,S2,S4,S6
S0,S2,S4,S5,Q8,Q8,Q8,Q8,T1,Q1,I0,Q4
L0,J3,L5,J8,T1,T6,S2,Z5,T0,T7
Q0,I2,I6,I0,I6,I6,Q2,Q4 
```
### Supplying custom input
Simply create a new file with the same input format as `examples/sample_input.txt` under `examples/` or anywhere else on your system and use the absolute path to the file when running the CLI (remember to add it to `.gitignore` if you decide to keep it elsewhere than `examples/`!)

## Usage 

Run the CLI directly with custom input or run example provided script:

### Run example

```bash
chmod +x scripts/run_example.sh

./scripts/run_example.sh
```

### Run custom input 
The CLI accepts an input file path as its first argument and an optional output file path as its second argument. If no output file path is provided, the output will be written to STDOUT.

```bash
# Output to STDOUT
tetris-engine /path/to/input.txt

# Output to file
tetris-engine /path/to/input.txt /path/to/output.txt
```

### Inspecting results
When outputting to STDOUT, results are displayed immediately. When outputting to a file:
```bash
cat /path/to/output.txt
```

## Testing & Linting

### Dependencies
The core library has **no external dependencies**. For testing and development:

- **Required for testing**: Only `pytest` and `pytest-cov` are needed to run the test suite
- **Optional for development**: `flake8` and `mypy` are included for code quality and type checking but do not affect the correctness of unit tests or the core library functionality

```bash
make test  # Runs pytest with coverage
make lint  # Runs flake8 and mypy for code quality
```

## Debug
ensure you are in the virtual environment before debugging
```bash
cd /path/to/tetris-project

. .venv_tetris/bin/activate
```

### Using pdb
```bash
# Debug the CLI with pdb
python -m pdb -m tetris.io /path/to/input.txt [/path/to/output.txt]
```

### Using the module directly
```bash
# Run the module directly for debugging
python -m tetris_engine.main /path/to/input.txt [/path/to/output.txt]
```

### Using the CLI command
```bash
# Debug the installed CLI command
tetris-engine /path/to/input.txt [/path/to/output.txt]
```
