import sys
from _typeshed import Read<PERSON><PERSON><PERSON><PERSON>, Unused
from typing import I<PERSON>, Any, BinaryIO, Final, Literal, NamedTuple, NoReturn, overload
from typing_extensions import Self, TypeAlias, deprecated

if sys.version_info >= (3, 9):
    __all__ = ["open", "Error", "Wave_read", "Wave_write"]
else:
    __all__ = ["open", "openfp", "Error", "Wave_read", "Wave_write"]

_File: TypeAlias = str | IO[bytes]

class Error(Exception): ...

WAVE_FORMAT_PCM: Final = 1

class _wave_params(NamedTuple):
    nchannels: int
    sampwidth: int
    framerate: int
    nframes: int
    comptype: str
    compname: str

class Wave_read:
    def __init__(self, f: _File) -> None: ...
    def __enter__(self) -> Self: ...
    def __exit__(self, *args: Unused) -> None: ...
    def __del__(self) -> None: ...
    def getfp(self) -> BinaryIO | None: ...
    def rewind(self) -> None: ...
    def close(self) -> None: ...
    def tell(self) -> int: ...
    def getnchannels(self) -> int: ...
    def getnframes(self) -> int: ...
    def getsampwidth(self) -> int: ...
    def getframerate(self) -> int: ...
    def getcomptype(self) -> str: ...
    def getcompname(self) -> str: ...
    def getparams(self) -> _wave_params: ...
    @deprecated("Deprecated in Python 3.13; removal scheduled for Python 3.15")
    def getmarkers(self) -> None: ...
    @deprecated("Deprecated in Python 3.13; removal scheduled for Python 3.15")
    def getmark(self, id: Any) -> NoReturn: ...
    def setpos(self, pos: int) -> None: ...
    def readframes(self, nframes: int) -> bytes: ...

class Wave_write:
    def __init__(self, f: _File) -> None: ...
    def __enter__(self) -> Self: ...
    def __exit__(self, *args: Unused) -> None: ...
    def __del__(self) -> None: ...
    def setnchannels(self, nchannels: int) -> None: ...
    def getnchannels(self) -> int: ...
    def setsampwidth(self, sampwidth: int) -> None: ...
    def getsampwidth(self) -> int: ...
    def setframerate(self, framerate: float) -> None: ...
    def getframerate(self) -> int: ...
    def setnframes(self, nframes: int) -> None: ...
    def getnframes(self) -> int: ...
    def setcomptype(self, comptype: str, compname: str) -> None: ...
    def getcomptype(self) -> str: ...
    def getcompname(self) -> str: ...
    def setparams(self, params: _wave_params | tuple[int, int, int, int, str, str]) -> None: ...
    def getparams(self) -> _wave_params: ...
    @deprecated("Deprecated in Python 3.13; removal scheduled for Python 3.15")
    def setmark(self, id: Any, pos: Any, name: Any) -> NoReturn: ...
    @deprecated("Deprecated in Python 3.13; removal scheduled for Python 3.15")
    def getmark(self, id: Any) -> NoReturn: ...
    @deprecated("Deprecated in Python 3.13; removal scheduled for Python 3.15")
    def getmarkers(self) -> None: ...
    def tell(self) -> int: ...
    def writeframesraw(self, data: ReadableBuffer) -> None: ...
    def writeframes(self, data: ReadableBuffer) -> None: ...
    def close(self) -> None: ...

@overload
def open(f: _File, mode: Literal["r", "rb"]) -> Wave_read: ...
@overload
def open(f: _File, mode: Literal["w", "wb"]) -> Wave_write: ...
@overload
def open(f: _File, mode: str | None = None) -> Any: ...

if sys.version_info < (3, 9):
    openfp = open
