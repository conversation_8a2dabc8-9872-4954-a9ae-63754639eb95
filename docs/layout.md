# Architecture & Design Notes

## Coordinate System
- **x-axis (columns):** 0 = leftmost → 9 = rightmost  
- **y-axis (rows):**    0 = bottom   → upwards

## Core Components
- **constants.py**  
  - `TETROMINO`: footprint offsets for each piece  
  - `WIDTH`: board width

- **core.py**  
  - `Board` class  
    - `.occupied`: set[(x,y)]  
    - `.row_count`: dict[row → count]  
    - `.col_heights`: List[int]  
    - `.drop()`: gravity, lock, clear, height return

- **io.py**  
  - `process_file()`: input → independent `Board` runs → output  
  - `main()`: CLI wrapper

## Testing
- Full pytest suite covering shapes, boundaries, stacking, clears, and I/O.
