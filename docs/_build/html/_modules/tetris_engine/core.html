

<!DOCTYPE html>
<html class="writer-html5" lang="en" data-content_root="../../">
<head>
  <meta charset="utf-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0" />
  <title>tetris_engine.core &mdash; Tetris Engine 0.1.0 documentation</title>
      <link rel="stylesheet" type="text/css" href="../../_static/pygments.css?v=b86133f3" />
      <link rel="stylesheet" type="text/css" href="../../_static/css/theme.css?v=e59714d7" />

  
      <script src="../../_static/jquery.js?v=5d32c60e"></script>
      <script src="../../_static/_sphinx_javascript_frameworks_compat.js?v=2cd50e6c"></script>
      <script src="../../_static/documentation_options.js?v=01f34227"></script>
      <script src="../../_static/doctools.js?v=9bcbadda"></script>
      <script src="../../_static/sphinx_highlight.js?v=dc90522c"></script>
    <script src="../../_static/js/theme.js"></script>
    <link rel="index" title="Index" href="../../genindex.html" />
    <link rel="search" title="Search" href="../../search.html" /> 
</head>

<body class="wy-body-for-nav"> 
  <div class="wy-grid-for-nav">
    <nav data-toggle="wy-nav-shift" class="wy-nav-side">
      <div class="wy-side-scroll">
        <div class="wy-side-nav-search" >

          
          
          <a href="../../index.html" class="icon icon-home">
            Tetris Engine
          </a>
<div role="search">
  <form id="rtd-search-form" class="wy-form" action="../../search.html" method="get">
    <input type="text" name="q" placeholder="Search docs" aria-label="Search docs" />
    <input type="hidden" name="check_keywords" value="yes" />
    <input type="hidden" name="area" value="default" />
  </form>
</div>
        </div><div class="wy-menu wy-menu-vertical" data-spy="affix" role="navigation" aria-label="Navigation menu">
              <p class="caption" role="heading"><span class="caption-text">Contents:</span></p>
<ul>
<li class="toctree-l1"><a class="reference internal" href="../../api.html">API Reference</a></li>
</ul>

        </div>
      </div>
    </nav>

    <section data-toggle="wy-nav-shift" class="wy-nav-content-wrap"><nav class="wy-nav-top" aria-label="Mobile navigation menu" >
          <i data-toggle="wy-nav-top" class="fa fa-bars"></i>
          <a href="../../index.html">Tetris Engine</a>
      </nav>

      <div class="wy-nav-content">
        <div class="rst-content">
          <div role="navigation" aria-label="Page navigation">
  <ul class="wy-breadcrumbs">
      <li><a href="../../index.html" class="icon icon-home" aria-label="Home"></a></li>
          <li class="breadcrumb-item"><a href="../index.html">Module code</a></li>
      <li class="breadcrumb-item active">tetris_engine.core</li>
      <li class="wy-breadcrumbs-aside">
      </li>
  </ul>
  <hr/>
</div>
          <div role="main" class="document" itemscope="itemscope" itemtype="http://schema.org/Article">
           <div itemprop="articleBody">
             
  <h1>Source code for tetris_engine.core</h1><div class="highlight"><pre>
<span></span><span class="sd">&quot;&quot;&quot;</span>
<span class="sd">Tetris game logic.</span>

<span class="sd">This module contains the core game logic for the Tetris Engine, including</span>
<span class="sd">the Board class that manages piece placement, line clearing, and height</span>
<span class="sd">calculation.</span>
<span class="sd">&quot;&quot;&quot;</span>
<span class="kn">from</span><span class="w"> </span><span class="nn">collections</span><span class="w"> </span><span class="kn">import</span> <span class="n">defaultdict</span>
<span class="kn">from</span><span class="w"> </span><span class="nn">typing</span><span class="w"> </span><span class="kn">import</span> <span class="n">Set</span><span class="p">,</span> <span class="n">Tuple</span>
<span class="kn">from</span><span class="w"> </span><span class="nn">tetris_engine.constants</span><span class="w"> </span><span class="kn">import</span> <span class="n">TETROMINO</span><span class="p">,</span> <span class="n">WIDTH</span>


<div class="viewcode-block" id="Board">
<a class="viewcode-back" href="../../api.html#tetris_engine.core.Board">[docs]</a>
<span class="k">class</span><span class="w"> </span><span class="nc">Board</span><span class="p">:</span>
<span class="w">    </span><span class="sd">&quot;&quot;&quot;</span>
<span class="sd">    Tetris board of fixed width and unbounded height.</span>
<span class="sd">    Tracks occupied cells, row counts, and column heights.</span>
<span class="sd">    &quot;&quot;&quot;</span>

    <span class="k">def</span><span class="w"> </span><span class="fm">__init__</span><span class="p">(</span><span class="bp">self</span><span class="p">,</span> <span class="n">width</span><span class="p">:</span> <span class="nb">int</span> <span class="o">=</span> <span class="n">WIDTH</span><span class="p">):</span>
        <span class="bp">self</span><span class="o">.</span><span class="n">width</span> <span class="o">=</span> <span class="n">width</span>
        <span class="bp">self</span><span class="o">.</span><span class="n">occupied</span><span class="p">:</span> <span class="n">Set</span><span class="p">[</span><span class="n">Tuple</span><span class="p">[</span><span class="nb">int</span><span class="p">,</span> <span class="nb">int</span><span class="p">]]</span> <span class="o">=</span> <span class="nb">set</span><span class="p">()</span>            <span class="c1"># set[(x, y)]</span>
        <span class="bp">self</span><span class="o">.</span><span class="n">row_count</span><span class="p">:</span> <span class="n">defaultdict</span><span class="p">[</span><span class="nb">int</span><span class="p">,</span> <span class="nb">int</span><span class="p">]</span> <span class="o">=</span> <span class="n">defaultdict</span><span class="p">(</span><span class="nb">int</span><span class="p">)</span>
        <span class="bp">self</span><span class="o">.</span><span class="n">col_heights</span> <span class="o">=</span> <span class="p">[</span><span class="o">-</span><span class="mi">1</span><span class="p">]</span> <span class="o">*</span> <span class="n">width</span>  <span class="c1"># max y per column</span>

<div class="viewcode-block" id="Board.drop">
<a class="viewcode-back" href="../../api.html#tetris_engine.core.Board.drop">[docs]</a>
    <span class="k">def</span><span class="w"> </span><span class="nf">drop</span><span class="p">(</span><span class="bp">self</span><span class="p">,</span> <span class="n">shape</span><span class="p">:</span> <span class="nb">str</span><span class="p">,</span> <span class="n">col</span><span class="p">:</span> <span class="nb">int</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="nb">int</span><span class="p">:</span>
<span class="w">        </span><span class="sd">&quot;&quot;&quot;</span>
<span class="sd">        Drop one Tetromino of type `shape` at column `col`.</span>
<span class="sd">        Returns new board height (max y + 1) after placement and any clears.</span>
<span class="sd">        &quot;&quot;&quot;</span>
        <span class="n">footprint</span> <span class="o">=</span> <span class="n">TETROMINO</span><span class="p">[</span><span class="n">shape</span><span class="p">]</span>

        <span class="c1"># Validate bounds</span>
        <span class="k">for</span> <span class="n">dx</span><span class="p">,</span> <span class="n">dy</span> <span class="ow">in</span> <span class="n">footprint</span><span class="p">:</span>
            <span class="n">x</span> <span class="o">=</span> <span class="n">col</span> <span class="o">+</span> <span class="n">dx</span>
            <span class="k">if</span> <span class="n">x</span> <span class="o">&lt;</span> <span class="mi">0</span> <span class="ow">or</span> <span class="n">x</span> <span class="o">&gt;=</span> <span class="bp">self</span><span class="o">.</span><span class="n">width</span><span class="p">:</span>
                <span class="k">raise</span> <span class="ne">ValueError</span><span class="p">(</span>
                    <span class="sa">f</span><span class="s2">&quot;Piece </span><span class="si">{</span><span class="n">shape</span><span class="si">}</span><span class="s2"> at column </span><span class="si">{</span><span class="n">col</span><span class="si">}</span><span class="s2"> goes out of bounds&quot;</span>
                <span class="p">)</span>

        <span class="c1"># 1) Compute landing y</span>
        <span class="n">landing_y</span> <span class="o">=</span> <span class="nb">max</span><span class="p">(</span>
            <span class="bp">self</span><span class="o">.</span><span class="n">col_heights</span><span class="p">[</span><span class="n">col</span> <span class="o">+</span> <span class="n">dx</span><span class="p">]</span> <span class="o">+</span> <span class="mi">1</span> <span class="o">-</span> <span class="n">dy</span>
            <span class="k">for</span> <span class="n">dx</span><span class="p">,</span> <span class="n">dy</span> <span class="ow">in</span> <span class="n">footprint</span>
        <span class="p">)</span>

        <span class="c1"># 2) Lock piece in place</span>
        <span class="k">for</span> <span class="n">dx</span><span class="p">,</span> <span class="n">dy</span> <span class="ow">in</span> <span class="n">footprint</span><span class="p">:</span>
            <span class="n">x</span><span class="p">,</span> <span class="n">y</span> <span class="o">=</span> <span class="n">col</span> <span class="o">+</span> <span class="n">dx</span><span class="p">,</span> <span class="n">landing_y</span> <span class="o">+</span> <span class="n">dy</span>
            <span class="bp">self</span><span class="o">.</span><span class="n">occupied</span><span class="o">.</span><span class="n">add</span><span class="p">((</span><span class="n">x</span><span class="p">,</span> <span class="n">y</span><span class="p">))</span>

        <span class="c1"># 3) Recompute row counts</span>
        <span class="bp">self</span><span class="o">.</span><span class="n">row_count</span><span class="o">.</span><span class="n">clear</span><span class="p">()</span>
        <span class="k">for</span> <span class="n">_</span><span class="p">,</span> <span class="n">y</span> <span class="ow">in</span> <span class="bp">self</span><span class="o">.</span><span class="n">occupied</span><span class="p">:</span>
            <span class="bp">self</span><span class="o">.</span><span class="n">row_count</span><span class="p">[</span><span class="n">y</span><span class="p">]</span> <span class="o">+=</span> <span class="mi">1</span>

        <span class="c1"># 4) Clear full rows</span>
        <span class="n">full_rows</span> <span class="o">=</span> <span class="nb">sorted</span><span class="p">(</span>
            <span class="n">y</span> <span class="k">for</span> <span class="n">y</span><span class="p">,</span> <span class="n">cnt</span> <span class="ow">in</span> <span class="bp">self</span><span class="o">.</span><span class="n">row_count</span><span class="o">.</span><span class="n">items</span><span class="p">()</span> <span class="k">if</span> <span class="n">cnt</span> <span class="o">==</span> <span class="bp">self</span><span class="o">.</span><span class="n">width</span>
        <span class="p">)</span>
        <span class="k">if</span> <span class="n">full_rows</span><span class="p">:</span>
            <span class="n">new_occ</span> <span class="o">=</span> <span class="nb">set</span><span class="p">()</span>
            <span class="k">for</span> <span class="n">x</span><span class="p">,</span> <span class="n">y</span> <span class="ow">in</span> <span class="bp">self</span><span class="o">.</span><span class="n">occupied</span><span class="p">:</span>
                <span class="k">if</span> <span class="n">y</span> <span class="ow">not</span> <span class="ow">in</span> <span class="n">full_rows</span><span class="p">:</span>
                    <span class="n">shift</span> <span class="o">=</span> <span class="nb">sum</span><span class="p">(</span><span class="mi">1</span> <span class="k">for</span> <span class="n">r</span> <span class="ow">in</span> <span class="n">full_rows</span> <span class="k">if</span> <span class="n">r</span> <span class="o">&lt;</span> <span class="n">y</span><span class="p">)</span>
                    <span class="n">new_occ</span><span class="o">.</span><span class="n">add</span><span class="p">((</span><span class="n">x</span><span class="p">,</span> <span class="n">y</span> <span class="o">-</span> <span class="n">shift</span><span class="p">))</span>
            <span class="bp">self</span><span class="o">.</span><span class="n">occupied</span> <span class="o">=</span> <span class="n">new_occ</span>

            <span class="c1"># Rebuild row counts after clear</span>
            <span class="bp">self</span><span class="o">.</span><span class="n">row_count</span><span class="o">.</span><span class="n">clear</span><span class="p">()</span>
            <span class="k">for</span> <span class="n">_</span><span class="p">,</span> <span class="n">y</span> <span class="ow">in</span> <span class="bp">self</span><span class="o">.</span><span class="n">occupied</span><span class="p">:</span>
                <span class="bp">self</span><span class="o">.</span><span class="n">row_count</span><span class="p">[</span><span class="n">y</span><span class="p">]</span> <span class="o">+=</span> <span class="mi">1</span>

        <span class="c1"># 5) Update column heights</span>
        <span class="bp">self</span><span class="o">.</span><span class="n">col_heights</span> <span class="o">=</span> <span class="p">[</span><span class="o">-</span><span class="mi">1</span><span class="p">]</span> <span class="o">*</span> <span class="bp">self</span><span class="o">.</span><span class="n">width</span>
        <span class="k">for</span> <span class="n">x</span><span class="p">,</span> <span class="n">y</span> <span class="ow">in</span> <span class="bp">self</span><span class="o">.</span><span class="n">occupied</span><span class="p">:</span>
            <span class="k">if</span> <span class="n">y</span> <span class="o">&gt;</span> <span class="bp">self</span><span class="o">.</span><span class="n">col_heights</span><span class="p">[</span><span class="n">x</span><span class="p">]:</span>
                <span class="bp">self</span><span class="o">.</span><span class="n">col_heights</span><span class="p">[</span><span class="n">x</span><span class="p">]</span> <span class="o">=</span> <span class="n">y</span>

        <span class="c1"># 6) Return current height</span>
        <span class="k">return</span> <span class="nb">max</span><span class="p">(</span><span class="bp">self</span><span class="o">.</span><span class="n">col_heights</span><span class="p">)</span> <span class="o">+</span> <span class="mi">1</span> <span class="k">if</span> <span class="bp">self</span><span class="o">.</span><span class="n">occupied</span> <span class="k">else</span> <span class="mi">0</span></div>
</div>

</pre></div>

           </div>
          </div>
          <footer>

  <hr/>

  <div role="contentinfo">
    <p>&#169; Copyright 2025, Tetris Engine Team.</p>
  </div>

  Built with <a href="https://www.sphinx-doc.org/">Sphinx</a> using a
    <a href="https://github.com/readthedocs/sphinx_rtd_theme">theme</a>
    provided by <a href="https://readthedocs.org">Read the Docs</a>.
   

</footer>
        </div>
      </div>
    </section>
  </div>
  <script>
      jQuery(function () {
          SphinxRtdTheme.Navigation.enable(true);
      });
  </script> 

</body>
</html>