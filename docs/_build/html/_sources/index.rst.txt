.. Tetris Engine documentation master file, created by
   sphinx-quickstart on Sun Jun 15 03:56:11 2025.
   You can adapt this file completely to your liking, but it should at least
   contain the root `toctree` directive.

Tetris Engine documentation
===========================

Tetris Engine is a simplified Tetris drop-and-clear simulator that processes sequences of Tetris piece drops and calculates the final board height.

Quick Start
===========

Install the package and run:

.. code-block:: bash

   tetris-engine input.txt [output.txt]

If no output file is specified, results are printed to STDOUT.

.. toctree::
   :maxdepth: 2
   :caption: Contents:

   api

API Reference
=============

The complete API documentation is available in the :doc:`api` section.

Indices and tables
==================

* :ref:`genindex`
* :ref:`modindex`
* :ref:`search`
