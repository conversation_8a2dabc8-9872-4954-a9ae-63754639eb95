

<!DOCTYPE html>
<html class="writer-html5" lang="en" data-content_root="./">
<head>
  <meta charset="utf-8" /><meta name="viewport" content="width=device-width, initial-scale=1" />

  <meta name="viewport" content="width=device-width, initial-scale=1.0" />
  <title>API Reference &mdash; Tetris Engine 0.1.0 documentation</title>
      <link rel="stylesheet" type="text/css" href="_static/pygments.css?v=b86133f3" />
      <link rel="stylesheet" type="text/css" href="_static/css/theme.css?v=e59714d7" />

  
      <script src="_static/jquery.js?v=5d32c60e"></script>
      <script src="_static/_sphinx_javascript_frameworks_compat.js?v=2cd50e6c"></script>
      <script src="_static/documentation_options.js?v=01f34227"></script>
      <script src="_static/doctools.js?v=9bcbadda"></script>
      <script src="_static/sphinx_highlight.js?v=dc90522c"></script>
    <script src="_static/js/theme.js"></script>
    <link rel="index" title="Index" href="genindex.html" />
    <link rel="search" title="Search" href="search.html" />
    <link rel="prev" title="Tetris Engine documentation" href="index.html" /> 
</head>

<body class="wy-body-for-nav"> 
  <div class="wy-grid-for-nav">
    <nav data-toggle="wy-nav-shift" class="wy-nav-side">
      <div class="wy-side-scroll">
        <div class="wy-side-nav-search" >

          
          
          <a href="index.html" class="icon icon-home">
            Tetris Engine
          </a>
<div role="search">
  <form id="rtd-search-form" class="wy-form" action="search.html" method="get">
    <input type="text" name="q" placeholder="Search docs" aria-label="Search docs" />
    <input type="hidden" name="check_keywords" value="yes" />
    <input type="hidden" name="area" value="default" />
  </form>
</div>
        </div><div class="wy-menu wy-menu-vertical" data-spy="affix" role="navigation" aria-label="Navigation menu">
              <p class="caption" role="heading"><span class="caption-text">Contents:</span></p>
<ul class="current">
<li class="toctree-l1 current"><a class="current reference internal" href="#">API Reference</a><ul>
<li class="toctree-l2"><a class="reference internal" href="#module-tetris_engine.core">Core Module</a><ul>
<li class="toctree-l3"><a class="reference internal" href="#tetris_engine.core.Board"><code class="docutils literal notranslate"><span class="pre">Board</span></code></a><ul>
<li class="toctree-l4"><a class="reference internal" href="#tetris_engine.core.Board.drop"><code class="docutils literal notranslate"><span class="pre">Board.drop()</span></code></a></li>
</ul>
</li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="#module-tetris_engine.constants">Constants Module</a></li>
<li class="toctree-l2"><a class="reference internal" href="#module-tetris_engine.io">IO Module</a><ul>
<li class="toctree-l3"><a class="reference internal" href="#tetris_engine.io.process_file"><code class="docutils literal notranslate"><span class="pre">process_file()</span></code></a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="#module-tetris_engine.main">Main Module</a><ul>
<li class="toctree-l3"><a class="reference internal" href="#tetris_engine.main.main"><code class="docutils literal notranslate"><span class="pre">main()</span></code></a></li>
</ul>
</li>
</ul>
</li>
</ul>

        </div>
      </div>
    </nav>

    <section data-toggle="wy-nav-shift" class="wy-nav-content-wrap"><nav class="wy-nav-top" aria-label="Mobile navigation menu" >
          <i data-toggle="wy-nav-top" class="fa fa-bars"></i>
          <a href="index.html">Tetris Engine</a>
      </nav>

      <div class="wy-nav-content">
        <div class="rst-content">
          <div role="navigation" aria-label="Page navigation">
  <ul class="wy-breadcrumbs">
      <li><a href="index.html" class="icon icon-home" aria-label="Home"></a></li>
      <li class="breadcrumb-item active">API Reference</li>
      <li class="wy-breadcrumbs-aside">
            <a href="_sources/api.rst.txt" rel="nofollow"> View page source</a>
      </li>
  </ul>
  <hr/>
</div>
          <div role="main" class="document" itemscope="itemscope" itemtype="http://schema.org/Article">
           <div itemprop="articleBody">
             
  <section id="api-reference">
<h1>API Reference<a class="headerlink" href="#api-reference" title="Link to this heading"></a></h1>
<p>This section contains the complete API documentation for the Tetris Engine.</p>
<section id="module-tetris_engine.core">
<span id="core-module"></span><h2>Core Module<a class="headerlink" href="#module-tetris_engine.core" title="Link to this heading"></a></h2>
<p>Tetris game logic.</p>
<p>This module contains the core game logic for the Tetris Engine, including
the Board class that manages piece placement, line clearing, and height calculation.</p>
<dl class="py class">
<dt class="sig sig-object py" id="tetris_engine.core.Board">
<em class="property"><span class="k"><span class="pre">class</span></span><span class="w"> </span></em><span class="sig-prename descclassname"><span class="pre">tetris_engine.core.</span></span><span class="sig-name descname"><span class="pre">Board</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">width</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><span class="pre">int</span></span><span class="w"> </span><span class="o"><span class="pre">=</span></span><span class="w"> </span><span class="default_value"><span class="pre">10</span></span></em><span class="sig-paren">)</span><a class="reference internal" href="_modules/tetris_engine/core.html#Board"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#tetris_engine.core.Board" title="Link to this definition"></a></dt>
<dd><p>Bases: <code class="xref py py-class docutils literal notranslate"><span class="pre">object</span></code></p>
<p>Tetris board of fixed width and unbounded height.
Tracks occupied cells, row counts, and column heights.</p>
<dl class="py method">
<dt class="sig sig-object py" id="tetris_engine.core.Board.drop">
<span class="sig-name descname"><span class="pre">drop</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">shape</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><span class="pre">str</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">col</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><span class="pre">int</span></span></em><span class="sig-paren">)</span> <span class="sig-return"><span class="sig-return-icon">&#x2192;</span> <span class="sig-return-typehint"><span class="pre">int</span></span></span><a class="reference internal" href="_modules/tetris_engine/core.html#Board.drop"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#tetris_engine.core.Board.drop" title="Link to this definition"></a></dt>
<dd><p>Drop one Tetromino of type <cite>shape</cite> at column <cite>col</cite>.
Returns new board height (max y + 1) after placement and any clears.</p>
</dd></dl>

</dd></dl>

</section>
<section id="module-tetris_engine.constants">
<span id="constants-module"></span><h2>Constants Module<a class="headerlink" href="#module-tetris_engine.constants" title="Link to this heading"></a></h2>
<p>Tetromino definitions and board constants.</p>
<p>Coordinate System:
- x-axis (columns): 0 = leftmost, increases rightward: “&gt;”.
- y-axis (rows):    0 = bottom,   increases upward: “^”.</p>
<p>Tetromino footprints are defined as offsets from the leftmost column.</p>
</section>
<section id="module-tetris_engine.io">
<span id="io-module"></span><h2>IO Module<a class="headerlink" href="#module-tetris_engine.io" title="Link to this heading"></a></h2>
<p>Input/Output operations for the Tetris Engine.</p>
<p>This module handles file processing and provides functions to read input files,
process Tetris sequences, and output results to files or STDOUT.</p>
<dl class="py function">
<dt class="sig sig-object py" id="tetris_engine.io.process_file">
<span class="sig-prename descclassname"><span class="pre">tetris_engine.io.</span></span><span class="sig-name descname"><span class="pre">process_file</span></span><span class="sig-paren">(</span><em class="sig-param"><span class="n"><span class="pre">input_path</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><span class="pre">str</span></span></em>, <em class="sig-param"><span class="n"><span class="pre">output_path</span></span><span class="p"><span class="pre">:</span></span><span class="w"> </span><span class="n"><span class="pre">str</span><span class="w"> </span><span class="p"><span class="pre">|</span></span><span class="w"> </span><span class="pre">None</span></span><span class="w"> </span><span class="o"><span class="pre">=</span></span><span class="w"> </span><span class="default_value"><span class="pre">None</span></span></em><span class="sig-paren">)</span> <span class="sig-return"><span class="sig-return-icon">&#x2192;</span> <span class="sig-return-typehint"><span class="pre">None</span></span></span><a class="reference internal" href="_modules/tetris_engine/io.html#process_file"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#tetris_engine.io.process_file" title="Link to this definition"></a></dt>
<dd><p>Reads each non-empty line of <cite>input_path</cite> as an independent game,
writes one final height per line to <cite>output_path</cite> or STDOUT if None.</p>
</dd></dl>

</section>
<section id="module-tetris_engine.main">
<span id="main-module"></span><h2>Main Module<a class="headerlink" href="#module-tetris_engine.main" title="Link to this heading"></a></h2>
<p>Main entry point for the Tetris Engine CLI.</p>
<dl class="py function">
<dt class="sig sig-object py" id="tetris_engine.main.main">
<span class="sig-prename descclassname"><span class="pre">tetris_engine.main.</span></span><span class="sig-name descname"><span class="pre">main</span></span><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="reference internal" href="_modules/tetris_engine/main.html#main"><span class="viewcode-link"><span class="pre">[source]</span></span></a><a class="headerlink" href="#tetris_engine.main.main" title="Link to this definition"></a></dt>
<dd><p>Main entry point for the tetris-engine CLI command.</p>
</dd></dl>

</section>
</section>


           </div>
          </div>
          <footer><div class="rst-footer-buttons" role="navigation" aria-label="Footer">
        <a href="index.html" class="btn btn-neutral float-left" title="Tetris Engine documentation" accesskey="p" rel="prev"><span class="fa fa-arrow-circle-left" aria-hidden="true"></span> Previous</a>
    </div>

  <hr/>

  <div role="contentinfo">
    <p>&#169; Copyright 2025, Tetris Engine Team.</p>
  </div>

  Built with <a href="https://www.sphinx-doc.org/">Sphinx</a> using a
    <a href="https://github.com/readthedocs/sphinx_rtd_theme">theme</a>
    provided by <a href="https://readthedocs.org">Read the Docs</a>.
   

</footer>
        </div>
      </div>
    </section>
  </div>
  <script>
      jQuery(function () {
          SphinxRtdTheme.Navigation.enable(true);
      });
  </script> 

</body>
</html>