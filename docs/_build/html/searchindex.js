Search.setIndex({"alltitles":{"API Reference":[[0,null],[1,"api-reference"]],"Constants Module":[[0,"module-tetris_engine.constants"]],"Contents:":[[1,null]],"Core Module":[[0,"module-tetris_engine.core"]],"IO Module":[[0,"module-tetris_engine.io"]],"Indices and tables":[[1,"indices-and-tables"]],"Main Module":[[0,"module-tetris_engine.main"]],"Quick Start":[[1,"quick-start"]],"Tetris Engine documentation":[[1,null]]},"docnames":["api","index"],"envversion":{"sphinx":65,"sphinx.domains.c":3,"sphinx.domains.changeset":1,"sphinx.domains.citation":1,"sphinx.domains.cpp":9,"sphinx.domains.index":1,"sphinx.domains.javascript":3,"sphinx.domains.math":2,"sphinx.domains.python":4,"sphinx.domains.rst":2,"sphinx.domains.std":2,"sphinx.ext.viewcode":1},"filenames":["api.rst","index.rst"],"indexentries":{"board (class in tetris_engine.core)":[[0,"tetris_engine.core.Board",false]],"drop() (tetris_engine.core.board method)":[[0,"tetris_engine.core.Board.drop",false]],"main() (in module tetris_engine.main)":[[0,"tetris_engine.main.main",false]],"module":[[0,"module-tetris_engine.constants",false],[0,"module-tetris_engine.core",false],[0,"module-tetris_engine.io",false],[0,"module-tetris_engine.main",false]],"process_file() (in module tetris_engine.io)":[[0,"tetris_engine.io.process_file",false]],"tetris_engine.constants":[[0,"module-tetris_engine.constants",false]],"tetris_engine.core":[[0,"module-tetris_engine.core",false]],"tetris_engine.io":[[0,"module-tetris_engine.io",false]],"tetris_engine.main":[[0,"module-tetris_engine.main",false]]},"objects":{"tetris_engine":[[0,0,0,"-","constants"],[0,0,0,"-","core"],[0,0,0,"-","io"],[0,0,0,"-","main"]],"tetris_engine.core":[[0,1,1,"","Board"]],"tetris_engine.core.Board":[[0,2,1,"","drop"]],"tetris_engine.io":[[0,3,1,"","process_file"]],"tetris_engine.main":[[0,3,1,"","main"]]},"objnames":{"0":["py","module","Python module"],"1":["py","class","Python class"],"2":["py","method","Python method"],"3":["py","function","Python function"]},"objtypes":{"0":"py:module","1":"py:class","2":"py:method","3":"py:function"},"terms":{"0":0,"1":0,"10":0,"If":1,"The":1,"after":0,"an":0,"ani":0,"ar":[0,1],"avail":1,"axi":0,"base":0,"board":[0,1],"bottom":0,"calcul":[0,1],"cell":0,"class":0,"clear":[0,1],"cli":0,"col":0,"column":0,"command":0,"complet":[0,1],"constant":1,"contain":0,"coordin":0,"core":1,"count":0,"defin":0,"definit":0,"document":0,"drop":[0,1],"each":0,"empti":0,"engin":0,"entri":0,"file":[0,1],"final":[0,1],"fix":0,"footprint":0,"from":0,"function":0,"game":0,"handl":0,"height":[0,1],"i":1,"includ":0,"increas":0,"independ":0,"index":1,"input":[0,1],"input_path":0,"instal":1,"int":0,"io":1,"leftmost":0,"line":0,"logic":0,"main":1,"manag":0,"max":0,"modul":1,"new":0,"non":0,"none":0,"object":0,"occupi":0,"offset":0,"one":0,"oper":0,"output":[0,1],"output_path":0,"packag":1,"page":1,"per":0,"piec":[0,1],"placement":0,"point":0,"print":1,"process":[0,1],"process_fil":0,"provid":0,"read":0,"result":[0,1],"return":0,"rightward":0,"row":0,"run":1,"search":1,"section":[0,1],"sequenc":[0,1],"shape":0,"simplifi":1,"simul":1,"sourc":0,"specifi":1,"stdout":[0,1],"str":0,"system":0,"tetri":0,"tetris_engin":0,"tetromino":0,"thi":0,"track":0,"txt":1,"type":0,"unbound":0,"upward":0,"width":0,"write":0,"x":0,"y":0},"titles":["API Reference","Tetris Engine documentation"],"titleterms":{"api":[0,1],"constant":0,"content":1,"core":0,"document":1,"engin":1,"indic":1,"io":0,"main":0,"modul":0,"quick":1,"refer":[0,1],"start":1,"tabl":1,"tetri":1}})