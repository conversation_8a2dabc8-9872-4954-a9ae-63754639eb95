# Architecture & Design Notes

## Coordinate System

- **x-axis (columns):** 0 = leftmost → 9 = rightmost  
- **y-axis (rows):**    0 = bottom   → upwards

## Core Components

- **constants.py:**  
  - `TETROMINO`: footprint offsets for each piece  
  - `WIDTH`: board width

- **core.py:**  
  - `Board` class  
    - `.occupied`: `set[(x,y)]` of locked cells  
    - `.row_count`: `dict[row → count]`  
    - `.col_heights`: `List[int]` of max y per column  
    - `.drop()`: gravity, placement, clear, height return

- **io.py:**  
  - `process_file()`: line-by-line input → independent `Board` runs → output heights  
  - `main()`: CLI wrapper using `argparse`

## Algorithm Implementation Trade-offs
Here, we use the following notation:
B = total number of occupied cells (blocks) on the board
R = number of full rows cleared in a single clear operation

- **Rebuild-on-clear (chosen):**  
  - After lock-in, recompute `row_count` (O(B)),  
    filter & shift `occupied` (O(B·R)),  
    recompute `col_heights` (O(B)).  
  - Simpler, fast for H≤100 (B≤1,000; R small).

- **Two-pointer approach (considered):**  
  - Sort rows & shift counts in linear passes,  
    overall O(B log B + R + B).  
  - More complex, only wins for very large boards/mass clears.

Although I attempted to consider an O(B log B + R + B) two-pointer algorithm (sorting rows and shifting counts with two indices), for a capped max height ≤100 (B ≤1,000 cells, R very small),
the straightforward O(B·R) clear logic is both simpler and faster in practice, which I finally adopted.

## Testing

- **pytest** suite under `tests/` covers all piece types, boundary drops, stacking, clears, and I/O.
