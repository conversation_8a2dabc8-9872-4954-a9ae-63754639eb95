["tests/test_core.py::test_full_board_clear", "tests/test_core.py::test_multi_row_clear_then_drop", "tests/test_core.py::test_no_clear_sequence", "tests/test_core.py::test_occupied_columns[I-3-cols0]", "tests/test_core.py::test_occupied_columns[Q-4-cols2]", "tests/test_core.py::test_occupied_columns[T-1-cols1]", "tests/test_core.py::test_right_boundary[I-6]", "tests/test_core.py::test_right_boundary[J-8]", "tests/test_core.py::test_right_boundary[L-8]", "tests/test_core.py::test_right_boundary[Q-8]", "tests/test_core.py::test_right_boundary[S-7]", "tests/test_core.py::test_right_boundary[T-7]", "tests/test_core.py::test_right_boundary[Z-7]", "tests/test_core.py::test_single_piece_drop[I-0-1]", "tests/test_core.py::test_single_piece_drop[J-0-3]", "tests/test_core.py::test_single_piece_drop[L-0-3]", "tests/test_core.py::test_single_piece_drop[Q-0-2]", "tests/test_core.py::test_single_piece_drop[S-0-2]", "tests/test_core.py::test_single_piece_drop[T-0-2]", "tests/test_core.py::test_single_piece_drop[Z-0-2]", "tests/test_core.py::test_stack_two_drops", "tests/test_io.py::test_process_file", "tests/test_io.py::test_process_file_empty", "tests/test_io.py::test_process_file_stdout"]