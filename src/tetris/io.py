import argparse
from tetris.core import Board

def process_file(input_path: str, output_path: str) -> None:
    """
    Reads each non-empty line of `input_path` as an independent game,
    writes one final height per line into `output_path`.
    """
    with open(input_path) as fin:
        lines = [l.strip() for l in fin if l.strip()]

    results = []
    for line in lines:
        board = Board()
        height = 0
        for token in line.split(','):
            shape, col = token[0], int(token[1])
            height = board.drop(shape, col)
        results.append(height)

    with open(output_path, 'w') as fout:
        for h in results:
            fout.write(f"{h}\n")

def main():
    parser = argparse.ArgumentParser(description="Simplified Tetris processor")
    parser.add_argument('input_file',  help='Path to input file')
    parser.add_argument('output_file', nargs='?', default='output.txt',
                        help='Path to output file')
    args = parser.parse_args()
    process_file(args.input_file, args.output_file)

if __name__ == '__main__':
    main()
