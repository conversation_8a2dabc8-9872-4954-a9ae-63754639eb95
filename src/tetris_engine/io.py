from tetris_engine.core import Board


def process_file(input_path: str, output_path: str = None) -> None:
    """
    Reads each non-empty line of `input_path` as an independent game,
    writes one final height per line to `output_path` or STDOUT if None.
    """
    with open(input_path) as fin:
        lines = [line.strip() for line in fin if line.strip()]

    results = []
    for line in lines:
        board = Board()
        height = 0
        for token in line.split(','):
            shape, col = token[0], int(token[1])
            height = board.drop(shape, col)
        results.append(height)

    # Write to file or STDOUT
    if output_path:
        with open(output_path, 'w') as fout:
            for h in results:
                fout.write(f"{h}\n")
    else:
        for h in results:
            print(h)
