from collections import defaultdict
from tetris_engine.constants import TETROMINO, WIDTH


class Board:
    """
    Tetris board of fixed width and unbounded height.
    Tracks occupied cells, row counts, and column heights.
    """

    def __init__(self, width: int = WIDTH):
        self.width = width
        self.occupied = set()            # set[(x, y)]
        self.row_count = defaultdict(int)
        self.col_heights = [-1] * width  # max y per column

    def drop(self, shape: str, col: int) -> int:
        """
        Drop one Tetromino of type `shape` at column `col`.
        Returns new board height (max y + 1) after placement and any clears.
        """
        footprint = TETROMINO[shape]

        # 1) Compute landing y
        landing_y = max(
            self.col_heights[col + dx] + 1 - dy
            for dx, dy in footprint
        )

        # 2) Lock piece in place
        for dx, dy in footprint:
            x, y = col + dx, landing_y + dy
            self.occupied.add((x, y))

        # 3) Recompute row counts
        self.row_count.clear()
        for _, y in self.occupied:
            self.row_count[y] += 1

        # 4) Clear full rows
        full_rows = sorted(y for y, cnt in self.row_count.items() if cnt == self.width)
        if full_rows:
            new_occ = set()
            for x, y in self.occupied:
                if y not in full_rows:
                    shift = sum(1 for r in full_rows if r < y)
                    new_occ.add((x, y - shift))
            self.occupied = new_occ

            # Rebuild row counts after clear
            self.row_count.clear()
            for _, y in self.occupied:
                self.row_count[y] += 1

        # 5) Update column heights
        self.col_heights = [-1] * self.width
        for x, y in self.occupied:
            if y > self.col_heights[x]:
                self.col_heights[x] = y

        # 6) Return current height
        return max(self.col_heights) + 1 if self.occupied else 0
