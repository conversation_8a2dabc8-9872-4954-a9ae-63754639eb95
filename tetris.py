"""
Simplified <PERSON><PERSON><PERSON> processor module.

Coordinate System:
- x-axis (columns): 0 = leftmost, increases rightward.
- y-axis (rows): 0 = bottom, increases upward.

Implementation Notes:
#   Here, we use the following notation:
#     B = total number of occupied cells (blocks) on the board
#     R = number of full rows cleared in a single clear operation
#   Thus:
#     - The simple rebuild method is O(B·R) at clear time.
#     - The two-pointer alternative would be O(B log B + R + B) overall.
- We use a simple rebuild-on-clear approach: after placing a piece, we recompute row counts,
  drop any full rows by filtering and shifting all occupied cells, then recompute column heights.
- Although we considered an O(B log B + R + B) two-pointer algorithm (sorting rows and shifting
  counts with two indices), for a capped max height ≤100 (B ≤1,000 cells, R very small),
  the straightforward O(B·R) clear logic is both simpler and faster in practice.
"""
import argparse
from collections import defaultdict

# Fixed footprints for each Tetromino (no rotation)
PIECES = {
    'I': [(0, 0), (1, 0), (2, 0), (3, 0)],
    'T': [(0, 1), (1, 1), (2, 1), (1, 0)],
    'S': [(1, 1), (2, 1), (0, 0), (1, 0)],
    'Z': [(0, 1), (1, 1), (1, 0), (2, 0)],
    'Q': [(0, 0), (1, 0), (0, 1), (1, 1)],
    'L': [(0, 0), (0, 1), (0, 2), (1, 0)],
    'J': [(1, 0), (1, 1), (1, 2), (0, 0)],
}
WIDTH = 10

class Board:
    """
    Tetris board: fixed width, unbounded height.
    Tracks occupied cells, row counts, and column heights.
    """
    def __init__(self, width=WIDTH):
        self.width = width
        self.occupied = set()            # set of (x, y)
        self.row_count = defaultdict(int)
        self.col_heights = [-1] * width  # max y per column

    def drop(self, shape: str, col: int) -> int:
        """
        Drop a piece of type `shape` at leftmost column `col`.
        Returns the current board height after placement and clears.
        """
        footprint = PIECES[shape]

        # Compute landing y so piece rests on floor or blocks
        landing_y = max(
            self.col_heights[col + dx] + 1 - dy
            for dx, dy in footprint
        )

        # Place blocks
        for dx, dy in footprint:
            x = col + dx
            y = landing_y + dy
            self.occupied.add((x, y))

        # Recompute row counts
        self.row_count.clear()
        for _, y in self.occupied:
            self.row_count[y] += 1

        # Find full rows
        full_rows = sorted(y for y, cnt in self.row_count.items() if cnt == self.width)
        if full_rows:
            # Simple clear: rebuild occupied set by shifting rows down
            new_occupied = set()
            for x, y in self.occupied:
                # count how many full rows are below y
                shift = sum(1 for r in full_rows if r < y)
                if y not in full_rows:
                    new_occupied.add((x, y - shift))
            self.occupied = new_occupied

            # Recompute row counts after clear
            self.row_count.clear()
            for _, y in self.occupied:
                self.row_count[y] += 1

        # Recompute column heights
        self.col_heights = [-1] * self.width
        for x, y in self.occupied:
            if y > self.col_heights[x]:
                self.col_heights[x] = y

        # Height is max y + 1, or 0 if empty
        return max(self.col_heights) + 1 if self.occupied else 0


def process_file(input_path: str, output_path: str) -> None:
    """
    Processes each input line as an independent game.
    Writes one height per line to output_path.
    """
    with open(input_path) as fin:
        lines = [l.strip() for l in fin if l.strip()]

    results = []
    for line in lines:
        board = Board()
        height = 0
        for token in line.split(','):
            height = board.drop(token[0], int(token[1]))
        results.append(height)

    with open(output_path, 'w') as fout:
        for h in results:
            fout.write(f"{h}\n")

if __name__ == '__main__':
    parser = argparse.ArgumentParser(description='Simplified Tetris processor')
    parser.add_argument('input_file')
    parser.add_argument('output_file', nargs='?', default='output.txt')
    args = parser.parse_args()
    process_file(args.input_file, args.output_file)
