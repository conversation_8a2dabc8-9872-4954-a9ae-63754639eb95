{"data_mtime": 1749977453, "dep_lines": [2, 4, 1, 2, 3, 5, 6, 1, 1, 1, 1], "dep_prios": [10, 5, 10, 20, 5, 5, 5, 5, 30, 30, 30], "dependencies": ["unittest.case", "collections.abc", "sys", "unittest", "_typeshed", "typing", "typing_extensions", "builtins", "_frozen_importlib", "abc", "types"], "hash": "f0048b43507e04666be8cb5d03b463f6c4f01679", "id": "unittest.result", "ignore_all": true, "interface_hash": "2b6d3feb1e655328f171bd5ea60a514c80d24587", "mtime": 1749978311, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "darwin", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "/Users/<USER>/Documents/code/tetris/.venv_tetris/lib/python3.11/site-packages/mypy/typeshed/stdlib/unittest/result.pyi", "plugin_data": null, "size": 2050, "suppressed": [], "version_id": "1.16.0"}