{"data_mtime": 1749977453, "dep_lines": [9, 3, 1, 2, 4, 5, 6, 7, 20, 1, 1, 1, 1, 1, 1], "dep_prios": [5, 5, 10, 10, 5, 5, 5, 5, 5, 5, 30, 30, 30, 30, 30], "dependencies": ["concurrent.futures._base", "collections.abc", "queue", "sys", "threading", "typing", "typing_extensions", "weakref", "types", "builtins", "_frozen_importlib", "_queue", "_thread", "_typeshed", "abc"], "hash": "4efbdb2795a16fc0fabc94378a5768834c431424", "id": "concurrent.futures.thread", "ignore_all": true, "interface_hash": "9a5552b821c11a39fd6f166b2165b6d59e8c87cf", "mtime": 1749978311, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "darwin", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "/Users/<USER>/Documents/code/tetris/.venv_tetris/lib/python3.11/site-packages/mypy/typeshed/stdlib/concurrent/futures/thread.pyi", "plugin_data": null, "size": 2329, "suppressed": [], "version_id": "1.16.0"}