{"data_mtime": 1749977453, "dep_lines": [3, 15, 16, 17, 27, 32, 1, 6, 7, 8, 13, 15, 32, 1, 1, 1], "dep_prios": [5, 10, 5, 5, 5, 20, 5, 10, 10, 5, 10, 20, 20, 5, 30, 30], "dependencies": ["collections.abc", "pluggy._tracing", "pluggy._callers", "pluggy._hooks", "pluggy._result", "importlib.metadata", "__future__", "inspect", "types", "typing", "warnings", "pluggy", "importlib", "builtins", "_frozen_importlib", "abc"], "hash": "73da70c7e1886f2243a1b50b1022d0861f1cc3f9", "id": "pluggy._manager", "ignore_all": true, "interface_hash": "d6a824dff579062b4b61b5dc4ecc3d4600a45be3", "mtime": 1749978310, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "darwin", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "/Users/<USER>/Documents/code/tetris/.venv_tetris/lib/python3.11/site-packages/pluggy/_manager.py", "plugin_data": null, "size": 20219, "suppressed": [], "version_id": "1.16.0"}