{"data_mtime": 1749977453, "dep_lines": [5, 1, 2, 3, 4, 6, 7, 1, 1, 1, 1, 1], "dep_prios": [5, 10, 10, 10, 5, 5, 5, 5, 30, 30, 30, 30], "dependencies": ["collections.abc", "sys", "types", "unittest", "_typeshed", "typing", "typing_extensions", "builtins", "_frozen_importlib", "abc", "unittest.case", "unittest.suite"], "hash": "8708a17bb91a9d021c6eb340a26e0c2e80ec9124", "id": "doctest", "ignore_all": true, "interface_hash": "89a2f914cbea66c40065f41e6b811e70dcc9f122", "mtime": 1749978311, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "darwin", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "/Users/<USER>/Documents/code/tetris/.venv_tetris/lib/python3.11/site-packages/mypy/typeshed/stdlib/doctest.pyi", "plugin_data": null, "size": 7796, "suppressed": [], "version_id": "1.16.0"}