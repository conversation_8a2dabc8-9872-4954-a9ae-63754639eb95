{"data_mtime": 1749977454, "dep_lines": [36, 6, 28, 29, 30, 31, 37, 38, 40, 41, 42, 4, 9, 11, 14, 16, 18, 19, 20, 21, 22, 28, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [5, 5, 10, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 10, 5, 10, 5, 5, 20, 5, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["_pytest.config.argparsing", "collections.abc", "_pytest.nodes", "_pytest._io", "_pytest.capture", "_pytest.config", "_pytest.deprecated", "_pytest.fixtures", "_pytest.main", "_pytest.stash", "_pytest.terminal", "__future__", "contextlib", "datetime", "io", "logging", "os", "pathlib", "re", "types", "typing", "_pytest", "builtins", "_collections_abc", "_frozen_importlib", "_io", "_pytest._io.terminalwriter", "_typeshed", "abc", "<PERSON><PERSON><PERSON><PERSON>", "enum", "genericpath", "pluggy", "pluggy._hooks", "pluggy._manager", "posixpath"], "hash": "110a1d1d892c7388adb4c015ad3ecba233c427fa", "id": "_pytest.logging", "ignore_all": true, "interface_hash": "5bba01c561f5221b4ce73894498c2edaeceece7f", "mtime": 1749978311, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "darwin", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "/Users/<USER>/Documents/code/tetris/.venv_tetris/lib/python3.11/site-packages/_pytest/logging.py", "plugin_data": null, "size": 35234, "suppressed": [], "version_id": "1.16.0"}