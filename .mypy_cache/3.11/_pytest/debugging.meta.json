{"data_mtime": 1749977454, "dep_lines": [23, 24, 8, 16, 17, 18, 19, 25, 26, 27, 5, 7, 10, 11, 12, 13, 14, 16, 112, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [5, 5, 5, 10, 5, 5, 5, 5, 5, 5, 5, 10, 10, 10, 10, 5, 10, 20, 20, 5, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["_pytest.config.argparsing", "_pytest.config.exceptions", "collections.abc", "_pytest.outcomes", "_pytest._code", "_pytest.capture", "_pytest.config", "_pytest.nodes", "_pytest.reports", "_pytest.runner", "__future__", "<PERSON><PERSON><PERSON><PERSON>", "functools", "sys", "types", "typing", "unittest", "_pytest", "pdb", "builtins", "_frozen_importlib", "_pytest._code.code", "_pytest._io", "_pytest._io.terminalwriter", "abc", "bdb", "cmd", "pluggy", "pluggy._hooks", "pluggy._manager"], "hash": "df76aee837e2fca0cbddcf6f878217f8ad9b3807", "id": "_pytest.debugging", "ignore_all": true, "interface_hash": "1384424b1bf5a8435f4335eab205f53063499f4c", "mtime": 1749978311, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "darwin", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "/Users/<USER>/Documents/code/tetris/.venv_tetris/lib/python3.11/site-packages/_pytest/debugging.py", "plugin_data": null, "size": 13947, "suppressed": [], "version_id": "1.16.0"}