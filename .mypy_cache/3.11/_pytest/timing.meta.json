{"data_mtime": 1749977454, "dep_lines": [9, 11, 12, 14, 17, 21, 87, 1, 1, 1, 1, 1], "dep_prios": [5, 10, 5, 5, 5, 25, 20, 5, 30, 30, 30, 30], "dependencies": ["__future__", "dataclasses", "datetime", "time", "typing", "pytest", "_pytest", "builtins", "_frozen_importlib", "_pytest.monkeypatch", "abc", "enum"], "hash": "62397c315610d1d4c1601d8d057197b63e81f0cc", "id": "_pytest.timing", "ignore_all": true, "interface_hash": "7ce5e2dfc7dd23bb33e5dc36e496a31f0e25ade2", "mtime": 1749978311, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "darwin", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "/Users/<USER>/Documents/code/tetris/.venv_tetris/lib/python3.11/site-packages/_pytest/timing.py", "plugin_data": null, "size": 3065, "suppressed": [], "version_id": "1.16.0"}