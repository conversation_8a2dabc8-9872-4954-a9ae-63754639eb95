{"data_mtime": 1749977453, "dep_lines": [4, 15, 17, 20, 36, 37, 38, 1, 3, 7, 8, 9, 13, 14, 17, 18, 19, 24, 26, 27, 28, 29, 31, 33, 34, 1, 1, 1, 1], "dep_prios": [5, 5, 10, 5, 5, 5, 5, 5, 10, 10, 5, 5, 10, 5, 20, 10, 10, 5, 5, 10, 10, 5, 5, 10, 10, 5, 30, 30, 30], "dependencies": ["collections.abc", "importlib.machinery", "importlib.util", "os.path", "_pytest.compat", "_pytest.outcomes", "_pytest.warning_types", "__future__", "atexit", "contextlib", "enum", "errno", "fnmatch", "functools", "importlib", "itertools", "os", "pathlib", "posixpath", "shutil", "sys", "types", "typing", "uuid", "warnings", "builtins", "_frozen_importlib", "_typeshed", "abc"], "hash": "df47459a4c19a297e19a55c7a73fd499ccdf3b86", "id": "_pytest.pathlib", "ignore_all": true, "interface_hash": "ba62930aad947c5b25068f45f33f0875bfc87c23", "mtime": 1749978311, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "darwin", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "/Users/<USER>/Documents/code/tetris/.venv_tetris/lib/python3.11/site-packages/_pytest/pathlib.py", "plugin_data": null, "size": 37622, "suppressed": [], "version_id": "1.16.0"}