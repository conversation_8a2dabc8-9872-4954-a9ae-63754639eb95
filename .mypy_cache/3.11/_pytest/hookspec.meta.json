{"data_mtime": 1749977454, "dep_lines": [24, 31, 8, 16, 26, 27, 32, 34, 35, 37, 38, 42, 44, 45, 6, 10, 11, 14, 20, 22, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [25, 25, 5, 5, 25, 25, 25, 25, 25, 25, 25, 25, 25, 25, 5, 5, 5, 5, 25, 25, 5, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["_pytest._code.code", "_pytest.config.argparsing", "collections.abc", "_pytest.deprecated", "_pytest.compat", "_pytest.config", "_pytest.fixtures", "_pytest.main", "_pytest.nodes", "_pytest.outcomes", "_pytest.python", "_pytest.reports", "_pytest.runner", "_pytest.terminal", "__future__", "pathlib", "typing", "pluggy", "pdb", "warnings", "builtins", "_frozen_importlib", "_pytest._code", "_pytest.warning_types", "abc", "bdb", "cmd", "enum", "os", "pluggy._hooks", "pluggy._manager"], "hash": "fe7fecc9ceb5563c991c8efa1eb61318e8722edb", "id": "_pytest.hookspec", "ignore_all": true, "interface_hash": "5889101ab5707b9303d8d0aeaaf7296d0d9133c6", "mtime": 1749978311, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "darwin", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "/Users/<USER>/Documents/code/tetris/.venv_tetris/lib/python3.11/site-packages/_pytest/hookspec.py", "plugin_data": null, "size": 42849, "suppressed": [], "version_id": "1.16.0"}