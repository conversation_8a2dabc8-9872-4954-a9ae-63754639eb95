{"data_mtime": 1749977454, "dep_lines": [26, 32, 7, 25, 29, 30, 31, 33, 35, 39, 40, 41, 42, 4, 6, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 23, 25, 46, 48, 1, 1, 1, 1, 1, 1], "dep_prios": [5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 10, 5, 10, 10, 10, 5, 10, 10, 10, 10, 10, 5, 10, 20, 20, 25, 5, 30, 30, 30, 30, 30], "dependencies": ["_pytest._code.code", "_pytest.config.argparsing", "collections.abc", "_pytest.outcomes", "_pytest._io", "_pytest.compat", "_pytest.config", "_pytest.fixtures", "_pytest.nodes", "_pytest.pathlib", "_pytest.python", "_pytest.python_api", "_pytest.warning_types", "__future__", "bdb", "contextlib", "functools", "inspect", "os", "pathlib", "platform", "re", "sys", "traceback", "types", "typing", "warnings", "_pytest", "doctest", "typing_extensions", "builtins", "_frozen_importlib", "_pytest._code", "_pytest._io.terminalwriter", "_pytest.main", "abc"], "hash": "b2b8ebcd3784d97d36950574b07c7ac5ad3557d3", "id": "_pytest.doctest", "ignore_all": true, "interface_hash": "b4ac98d7e0ff1f0bfcc61ecfe47371dcb41704ee", "mtime": 1749978311, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "darwin", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "/Users/<USER>/Documents/code/tetris/.venv_tetris/lib/python3.11/site-packages/_pytest/doctest.py", "plugin_data": null, "size": 26259, "suppressed": [], "version_id": "1.16.0"}