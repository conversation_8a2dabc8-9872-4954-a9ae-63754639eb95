{"data_mtime": 1749977454, "dep_lines": [24, 27, 7, 19, 23, 28, 29, 33, 43, 44, 4, 6, 8, 9, 10, 11, 12, 23, 1, 1, 1, 1, 1, 1], "dep_prios": [5, 5, 5, 5, 10, 5, 5, 5, 25, 25, 5, 10, 10, 10, 10, 10, 5, 20, 5, 30, 30, 30, 30, 30], "dependencies": ["_pytest._code.code", "_pytest.config.argparsing", "collections.abc", "_pytest.reports", "_pytest.timing", "_pytest.deprecated", "_pytest.nodes", "_pytest.outcomes", "_pytest.main", "_pytest.terminal", "__future__", "bdb", "dataclasses", "os", "sys", "types", "typing", "_pytest", "builtins", "_frozen_importlib", "_pytest._code", "_pytest.config", "_typeshed", "abc"], "hash": "c7a6447031de8b7d3740fdfc08775cb23948f5a2", "id": "_pytest.runner", "ignore_all": true, "interface_hash": "74613e4b53510f0147fa80a5447b4bef3d2cff7c", "mtime": 1749978311, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "darwin", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "/Users/<USER>/Documents/code/tetris/.venv_tetris/lib/python3.11/site-packages/_pytest/runner.py", "plugin_data": null, "size": 19543, "suppressed": [], "version_id": "1.16.0"}